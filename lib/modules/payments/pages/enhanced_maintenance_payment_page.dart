import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/colors.dart';
import '../../../core/theme/style_guide.dart';
import '../../../core/widgets/app_button.dart';

import '../../../core/services/sso_flutter_maintenance_service.dart';
import '../../../core/services/keycloak_service.dart';
import '../../../utils/storage/sso_storage.dart';
import '../../../data/models/recharge_models.dart';
import '../../household/maintenance/widgets/maintenance_billing_card.dart';
import '../widgets/payment_amount_breakdown.dart';
import '../widgets/payment_step_indicator.dart';

import 'payment_history_page.dart';

enum MaintenanceStep {
  ACCOUNT_DETAILS,
  PAYMENT_CONFIRMATION,
  PAYMENT_PROCESSING,
}

class EnhancedMaintenancePaymentPage extends ConsumerStatefulWidget {
  final Map<String, dynamic>? preSelectedAccount;

  const EnhancedMaintenancePaymentPage({
    super.key,
    this.preSelectedAccount,
  });

  @override
  ConsumerState<EnhancedMaintenancePaymentPage> createState() =>
      _EnhancedMaintenancePaymentPageState();
}

class _EnhancedMaintenancePaymentPageState
    extends ConsumerState<EnhancedMaintenancePaymentPage>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _societyNameController = TextEditingController();
  final _accountNumberController = TextEditingController();
  final _confirmAccountController = TextEditingController();
  final _ifscController = TextEditingController();
  final _amountController = TextEditingController();
  final _panController = TextEditingController();
  final _noteController = TextEditingController();

  final _emailFocusNode = FocusNode();
  final _amountFocusNode = FocusNode();
  final PageController _pageController = PageController();

  MaintenanceStep _currentStep = MaintenanceStep.ACCOUNT_DETAILS;
  Timer? _debounce;

  // SSO-Flutter style variables
  bool _showPanField = false;
  double _totalPayableAmount = 0;
  bool _isAmountValid = false;
  bool _termsAccepted = false;

  // Razorpay integration
  late Razorpay _razorpay;
  bool _isPaymentProcessing = false;

  bool _isLoadingBills = false;
  bool _isLoadingAccount = false;
  bool _isLoadingAccountSuggestions = false;
  bool _isCalculating = false;
  bool _isProcessingPayment = false;

  Map<String, dynamic>? _accountDetails;
  Map<String, dynamic>? _amountCalculation;
  List<Map<String, dynamic>> _accountSuggestions = [];

  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeRazorpay();

    // Auto-populate form if account is pre-selected (SSO-Flutter compatible)
    if (widget.preSelectedAccount != null) {
      _populateFormWithSelectedAccount(widget.preSelectedAccount!);
    } else {
      _loadUserData();
    }

    // Listen to email changes for autocomplete
    _emailController.addListener(_onEmailChanged);

    // Listen to amount field focus changes for UI updates
    _amountFocusNode.addListener(() {
      setState(() {
        // This will trigger a rebuild to update the underline color
      });
    });
  }

  void _initializeRazorpay() {
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _societyNameController.dispose();
    _accountNumberController.dispose();
    _confirmAccountController.dispose();
    _ifscController.dispose();
    _amountController.dispose();
    _panController.dispose();
    _noteController.dispose();
    _emailFocusNode.dispose();
    _amountFocusNode.dispose();
    _pageController.dispose();
    _debounce?.cancel();
    _slideController.dispose();
    _razorpay.clear();
    super.dispose();
  }

  // Format account number like SSO-Flutter
  String _formatAccountNumber() {
    if (_accountDetails != null && _accountDetails!['bank_account'] != null) {
      final accountNumber =
          _accountDetails!['bank_account']['account_number']?.toString() ?? '';
      final ifscCode =
          _accountDetails!['bank_account']['ifsc_code']?.toString() ?? '';

      if (accountNumber.isNotEmpty && accountNumber.length > 8) {
        final maskedNumber =
            '${accountNumber.substring(0, 4)}${'*' * (accountNumber.length - 8)}${accountNumber.substring(accountNumber.length - 4)}';
        return '$maskedNumber - $ifscCode';
      }
    }
    return '******8757';
  }

  // Show note dialog (SSO-Flutter style)
  void _showNoteDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with close button
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Add a note',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.grey[100],
                      foregroundColor: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Note input field
              TextFormField(
                controller: _noteController,
                decoration: const InputDecoration(
                  hintText: 'Enter your note here...',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.all(16),
                ),
                maxLines: 3,
                maxLength: 200,
                onFieldSubmitted: (value) {
                  setState(() {});
                  Navigator.pop(context);
                },
              ),
              const SizedBox(height: 20),

              // Save button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {});
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Save',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Show PAN dialog (SSO-Flutter style) with enhanced validation
  void _showPanDialog() {
    final TextEditingController tempPanController =
        TextEditingController(text: _panController.text);
    bool isPanValid = false;
    String? panError;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) {
          // PAN validation function
          void validatePan(String value) {
            final panRegex = RegExp(r'^[A-Z]{5}[0-9]{4}[A-Z]{1}$');
            setModalState(() {
              if (value.isEmpty) {
                isPanValid = false;
                panError = null;
              } else if (value.length < 10) {
                isPanValid = false;
                panError = 'PAN must be 10 characters';
              } else if (!panRegex.hasMatch(value)) {
                isPanValid = false;
                panError = 'Invalid PAN format (e.g., **********)';
              } else {
                isPanValid = true;
                panError = null;
              }
            });
          }

          // Initial validation
          if (tempPanController.text.isNotEmpty) {
            validatePan(tempPanController.text);
          }

          return Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with close button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Add society PAN number',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.close),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey[100],
                          foregroundColor: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // PAN format info
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline,
                            color: Colors.blue[600], size: 20),
                        const SizedBox(width: 8),
                        const Expanded(
                          child: Text(
                            'Format: 5 letters + 4 digits + 1 letter (e.g., **********)',
                            style: TextStyle(fontSize: 12, color: Colors.blue),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),

                  // PAN input field
                  TextFormField(
                    controller: tempPanController,
                    decoration: InputDecoration(
                      hintText: 'Enter society PAN number',
                      border: const OutlineInputBorder(),
                      contentPadding: const EdgeInsets.all(16),
                      errorText: panError,
                      suffixIcon: isPanValid
                          ? const Icon(Icons.check_circle, color: Colors.green)
                          : null,
                    ),
                    textCapitalization: TextCapitalization.characters,
                    maxLength: 10,
                    onChanged: validatePan,
                    onFieldSubmitted: (value) {
                      if (isPanValid) {
                        _panController.text = tempPanController.text;
                        setState(() {});
                        Navigator.pop(context);
                      }
                    },
                  ),
                  const SizedBox(height: 20),

                  // Save button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: isPanValid
                          ? () {
                              _panController.text = tempPanController.text;
                              setState(() {});
                              Navigator.pop(context);
                            }
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            isPanValid ? AppColors.primary : Colors.grey[300],
                        foregroundColor:
                            isPanValid ? Colors.white : Colors.grey[600],
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Save',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // Show amount breakdown dialog
  void _showAmountBreakdownDialog() {
    final baseAmount = double.tryParse(_amountController.text) ?? 0;
    final processingFees = _calculateProcessingFees(baseAmount);
    final totalAmount = baseAmount + processingFees;

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with close button
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Amount Breakdown',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                  style: IconButton.styleFrom(
                    backgroundColor: Colors.grey[100],
                    foregroundColor: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Amount breakdown
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  _buildBreakdownRow('Base Amount', baseAmount, false),
                  const Divider(),
                  _buildBreakdownRow('Processing Fees', processingFees, false),
                  const Divider(thickness: 2),
                  _buildBreakdownRow('Total Amount Payable', totalAmount, true),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Info note
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Processing fees may vary based on payment method and amount.',
                      style: TextStyle(fontSize: 12, color: Colors.blue),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // Close button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Got it',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build breakdown rows
  Widget _buildBreakdownRow(String label, double amount, bool isTotal) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? Colors.black : Colors.grey[700],
            ),
          ),
          Text(
            _formatCurrency(amount),
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
              color: isTotal ? AppColors.primary : Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  // Calculate processing fees (can be customized based on business logic)
  double _calculateProcessingFees(double baseAmount) {
    if (baseAmount == 0) return 0;
    // Example: 2% processing fee with minimum ₹10 and maximum ₹100
    final feePercentage = baseAmount * 0.02;
    return feePercentage.clamp(10.0, 100.0);
  }

  // Format currency with ₹ symbol and comma separators
  String _formatCurrency(double amount) {
    if (amount == 0) return '₹0';
    final formatter = NumberFormat('#,##,###', 'en_IN');
    return '₹${formatter.format(amount)}';
  }

  // Razorpay Payment Handlers
  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    print('✅ Payment Success: ${response.paymentId}');
    _completePayment(response.paymentId!, response.orderId, response.signature);
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    print('❌ Payment Error: ${response.code} - ${response.message}');
    setState(() {
      _isPaymentProcessing = false;
      _currentStep = MaintenanceStep.PAYMENT_CONFIRMATION;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Payment failed: ${response.message}'),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: () => _processPayment(),
        ),
      ),
    );
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    print('🔗 External Wallet: ${response.walletName}');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Redirected to ${response.walletName}'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  Future<void> _completePayment(
      String paymentId, String? orderId, String? signature) async {
    try {
      await SSOFlutterMaintenanceService.completeSocietyPayment(
        paymentId: paymentId,
        orderId: orderId ?? '',
        totalPayableAmount: _totalPayableAmount,
        accountId: _accountDetails?['account_id'] ?? '',
      );

      setState(() {
        _currentStep = MaintenanceStep.PAYMENT_PROCESSING;
      });

      _showPaymentSuccessDialog(paymentId);
    } catch (e) {
      print('❌ Error completing payment: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error completing payment: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showPaymentSuccessDialog(String paymentId) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 28),
            SizedBox(width: 8),
            Text('Payment Successful'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Payment ID: $paymentId'),
            const SizedBox(height: 8),
            Text('Amount: ${_formatCurrency(_totalPayableAmount)}'),
            const SizedBox(height: 8),
            const Text(
                'Your maintenance payment has been processed successfully.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Go back to previous screen
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  /// Auto-populate form with pre-selected account data (SSO-Flutter compatible)
  void _populateFormWithSelectedAccount(Map<String, dynamic> accountData) {
    try {
      print(
          '🏦 [UI] Auto-populating form with selected account: ${accountData['business_name']}');

      setState(() {
        // Populate form fields with account data
        _emailController.text = accountData['email'] ?? '';
        _societyNameController.text =
            accountData['business_name'] ?? accountData['society_name'] ?? '';
        _accountNumberController.text = accountData['account_number'] ?? '';
        _confirmAccountController.text = accountData['account_number'] ?? '';
        _ifscController.text = accountData['ifsc_code'] ?? '';

        // Set account details for payment processing
        _accountDetails = accountData;
        _isLoadingAccount = false;

        // Account is already selected, stay on account details step
        _currentStep = MaintenanceStep.ACCOUNT_DETAILS;
      });

      // Show success message after build completes
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                'Account loaded: ${accountData['business_name'] ?? 'Selected Account'}'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      });

      print('✅ [UI] Form auto-populated successfully');
    } catch (e) {
      print('❌ [UI] Error auto-populating form: $e');
      // Fallback to normal user data loading
      _loadUserData();
    }
  }

  void _setupAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeInOut,
    ));
  }

  void _onEmailChanged() {
    if (_debounce?.isActive ?? false) _debounce?.cancel();

    _debounce = Timer(const Duration(milliseconds: 800), () {
      final email = _emailController.text.trim();
      if (email.isNotEmpty && email.contains('@')) {
        _getAccountSuggestions(email);
      }
    });
  }

  Future<void> _loadUserData() async {
    try {
      // Try to get user data from authentication
      final userData = await KeycloakService.getUserData();
      if (userData != null && userData['email'] != null) {
        _emailController.text = userData['email'];
        _getAccountSuggestions(userData['email']);
      }
    } catch (e) {
      print('Error loading user data: $e');
    }
  }

  String _getErrorMessage(String error) {
    if (error.contains('Authentication failed')) {
      return 'Please log in again to view maintenance bills.';
    } else if (error.contains('Unit ID required')) {
      return 'Unable to find your unit information. Please contact support.';
    } else if (error.contains('404')) {
      return 'Maintenance billing service is currently unavailable.';
    } else {
      return 'Failed to load maintenance bills. Please try again.';
    }
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.receipt_long_outlined,
              size: 40,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 24),

          // Title
          Text(
            'No Maintenance Bills Found',
            style: AppStyleGuide.headingMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),

          // Description
          Text(
            'You\'re all caught up! No pending maintenance bills at the moment.',
            style: AppStyleGuide.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),

          Text(
            'Bills will appear here when they become available.',
            style: AppStyleGuide.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),

          // Action buttons
          Column(
            children: [
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () {
                    // Refresh functionality can be added here if needed
                  },
                  icon: const Icon(Icons.refresh, size: 18),
                  label: const Text('Refresh'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primary,
                    side: BorderSide(color: AppColors.primary),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    // Navigate to payment history or contact support
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PaymentHistoryPage(),
                      ),
                    );
                  },
                  icon: const Icon(Icons.history, size: 18),
                  label: const Text('View History'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _getAccountSuggestions(String email) async {
    setState(() {
      _isLoadingAccountSuggestions = true;
    });

    try {
      // Use SSO-Flutter compatible API to get account suggestions
      final suggestions =
          await SSOFlutterMaintenanceService.getAccountSuggestions(
              email: email);

      setState(() {
        _accountSuggestions = suggestions;
        _isLoadingAccountSuggestions = false;
      });
    } catch (e) {
      print('Error getting account suggestions: $e');
      setState(() {
        _isLoadingAccountSuggestions = false;
      });
    }
  }

  Future<void> _selectAccount(Map<String, dynamic> account) async {
    setState(() {
      _isLoadingAccount = true;
    });

    try {
      // Auto-populate fields from selected account
      _emailController.text = account['email'] ?? '';
      _societyNameController.text =
          account['business_name'] ?? account['society_name'] ?? '';
      _accountNumberController.text = account['account_number'] ?? '';
      _confirmAccountController.text = account['account_number'] ?? '';
      _ifscController.text = account['ifsc_code'] ?? '';

      setState(() {
        _accountDetails = account;
        _isLoadingAccount = false;
      });

      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text('Account details loaded for ${account['business_name']}'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _isLoadingAccount = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading account: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _validateAndCalculateAmount(String value) {
    final amount = double.tryParse(value);

    setState(() {
      if (amount == null || amount <= 0 || amount > 100000) {
        _isAmountValid = false;
        _totalPayableAmount = 0;
        _showPanField = false;
      } else {
        _isAmountValid = true;
        _showPanField = amount >= 50000;
        // Calculate in background
        _calculateAmountInBackground(amount);
      }
    });
  }

  Future<void> _calculateAmountInBackground(double amount) async {
    try {
      final calculation =
          await SSOFlutterMaintenanceService.calculateTotalSocietyPaymentAmount(
        amount: amount,
      );

      if (mounted) {
        setState(() {
          _amountCalculation = calculation;
          _totalPayableAmount = calculation['amount']?.toDouble() ??
              (amount + _calculateProcessingFees(amount));
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          // Fallback: calculate total with processing fees locally
          _totalPayableAmount = amount + _calculateProcessingFees(amount);
        });
      }
      print('Error calculating amount: $e');
    }
  }

  void _nextStep() {
    switch (_currentStep) {
      case MaintenanceStep.ACCOUNT_DETAILS:
        if (_accountDetails != null &&
            _formKey.currentState!.validate() &&
            _isAmountValid) {
          setState(() {
            _currentStep = MaintenanceStep.PAYMENT_CONFIRMATION;
          });
          _pageController.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }
        break;
      case MaintenanceStep.PAYMENT_CONFIRMATION:
        _processPayment();
        break;
      case MaintenanceStep.PAYMENT_PROCESSING:
        break;
    }
  }

  void _previousStep() {
    switch (_currentStep) {
      case MaintenanceStep.ACCOUNT_DETAILS:
        // Can't go back from first step
        break;
      case MaintenanceStep.PAYMENT_CONFIRMATION:
        setState(() {
          _currentStep = MaintenanceStep.ACCOUNT_DETAILS;
        });
        _pageController.previousPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
        break;
      case MaintenanceStep.PAYMENT_PROCESSING:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Maintenance Payment'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PaymentHistoryPage(),
                ),
              );
            },
            tooltip: 'Payment History',
          ),
        ],
      ),
      body: Column(
        children: [
          // Progress Indicator
          _buildProgressIndicator(),

          // Main Content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildAccountDetailsStep(),
                _buildPaymentConfirmationStep(),
              ],
            ),
          ),

          // Navigation Buttons
          if (_currentStep != MaintenanceStep.PAYMENT_PROCESSING)
            _buildNavigationButtons(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      color: AppColors.primary,
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          _buildStepIndicator(0, 'Account', _currentStep.index >= 0),
          Expanded(child: _buildProgressLine(_currentStep.index >= 1)),
          _buildStepIndicator(1, 'Amount', _currentStep.index >= 1),
          Expanded(child: _buildProgressLine(_currentStep.index >= 2)),
          _buildStepIndicator(2, 'Payment', _currentStep.index >= 2),
        ],
      ),
    );
  }

  Widget _buildStepIndicator(int step, String label, bool isActive) {
    return Column(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: isActive ? Colors.white : Colors.white.withOpacity(0.3),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              '${step + 1}',
              style: TextStyle(
                color: isActive ? AppColors.primary : Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressLine(bool isActive) {
    return Container(
      height: 2,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: isActive ? Colors.white : Colors.white.withOpacity(0.3),
        borderRadius: BorderRadius.circular(1),
      ),
    );
  }

  Widget _buildAccountDetailsStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Account Avatar and Details (SSO-Flutter style)
            Center(
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: AppColors.primary,
                    child: const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _accountDetails?['account_details']?['business_name'] ??
                        'GOOD LUCK CO OP SOCIETYY',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _formatAccountNumber(),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),

            // Google Pay Style Amount Input Field
            Container(
              padding: const EdgeInsets.symmetric(vertical: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.baseline,
                textBaseline: TextBaseline.alphabetic,
                children: [
                  // Currency symbol
                  Text(
                    '₹',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.w400,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Amount input field
                  Flexible(
                    child: IntrinsicWidth(
                      child: TextFormField(
                        controller: _amountController,
                        focusNode: _amountFocusNode,
                        onChanged: (value) {
                          _validateAndCalculateAmount(value);
                        },
                        maxLength: 7,
                        style: const TextStyle(
                          fontSize: 52,
                          fontWeight: FontWeight.w300,
                          color: Colors.black87,
                          letterSpacing: -1,
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        textAlign: TextAlign.center,
                        decoration: InputDecoration(
                          counterText: '',
                          hintText: '0',
                          hintStyle: TextStyle(
                            fontSize: 52,
                            fontWeight: FontWeight.w300,
                            color: Colors.grey[400],
                            letterSpacing: -1,
                          ),
                          border: InputBorder.none,
                          enabledBorder: InputBorder.none,
                          focusedBorder: InputBorder.none,
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Subtle underline for focus indication
            Container(
              height: 2,
              margin: const EdgeInsets.symmetric(horizontal: 40),
              decoration: BoxDecoration(
                color: _amountFocusNode.hasFocus
                    ? AppColors.primary.withOpacity(0.3)
                    : Colors.grey[300],
                borderRadius: BorderRadius.circular(1),
              ),
            ),

            // Amount validation message
            if (_amountController.text.isNotEmpty && !_isAmountValid)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  'Please enter amount between ₹1 and ₹1,00,000',
                  style: TextStyle(
                    color: Colors.red[600],
                    fontSize: 14,
                  ),
                ),
              ),
            const SizedBox(height: 24),

            // Options Container (SSO-Flutter style)
            Container(
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                children: [
                  // Add a note
                  ListTile(
                    onTap: () => _showNoteDialog(),
                    leading: const Icon(Icons.note_add),
                    title: const Text('Add a note'),
                    subtitle: Text(
                      _noteController.text.isEmpty ? '' : _noteController.text,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 18),
                  ),
                  const Divider(indent: 15, endIndent: 15),

                  // Add society PAN number
                  ListTile(
                    onTap: () => _showPanDialog(),
                    leading: const Icon(Icons.fact_check),
                    title: const Text('Add society PAN number'),
                    subtitle: Text(
                      _panController.text.isEmpty
                          ? (_showPanField
                              ? 'Required for amount above ₹50k'
                              : 'Optional')
                          : _panController.text,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 18),
                  ),
                  const Divider(indent: 15, endIndent: 15),

                  // Total amount (tappable)
                  ListTile(
                    onTap: () => _showAmountBreakdownDialog(),
                    leading: const Icon(Icons.receipt),
                    title: Text(
                        'Total amount ${_formatCurrency(_totalPayableAmount)}'),
                    subtitle: const Text('Includes processing fees'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 18),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Processing time notice
            Text(
              'It may take up to 48 working hours for the amount to be reflected in the bank account.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountCalculationStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Payment Amount',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Enter the amount you want to pay',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),

          // Amount Input
          TextFormField(
            controller: _amountController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'Amount (₹)',
              prefixIcon: const Icon(Icons.currency_rupee),
              border: const OutlineInputBorder(),
              suffixIcon: _isCalculating
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: Padding(
                        padding: EdgeInsets.all(8.0),
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : null,
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter amount';
              }
              final amount = double.tryParse(value);
              if (amount == null || amount <= 0) {
                return 'Please enter a valid amount';
              }
              return null;
            },
          ),
          const SizedBox(height: 24),

          // Amount Calculation Display
          if (_amountCalculation != null)
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Payment Breakdown',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildAmountRow(
                        'Base Amount', _amountCalculation!['base_amount']),
                    _buildAmountRow(
                        'Processing Fee', _amountCalculation!['fee']),
                    _buildAmountRow('GST', _amountCalculation!['gst']),
                    const Divider(),
                    _buildAmountRow(
                      'Total Amount',
                      _amountCalculation!['amount'],
                      isTotal: true,
                    ),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 24),

          // PAN Input (if amount >= 50000)
          if (_amountCalculation != null &&
              _amountCalculation!['amount'] >= 50000)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'PAN Required for amounts ≥ ₹50,000',
                  style: TextStyle(
                    color: Colors.orange,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _panController,
                  decoration: const InputDecoration(
                    labelText: 'PAN Number',
                    prefixIcon: Icon(Icons.credit_card),
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (_amountCalculation!['amount'] >= 50000 &&
                        (value == null || value.isEmpty)) {
                      return 'PAN is required for amounts ≥ ₹50,000';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
              ],
            ),

          // Notes
          TextFormField(
            controller: _noteController,
            maxLines: 3,
            decoration: const InputDecoration(
              labelText: 'Notes (Optional)',
              prefixIcon: Icon(Icons.note),
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountRow(String label, dynamic amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '₹${amount?.toStringAsFixed(2) ?? '0.00'}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentConfirmationStep() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Society Avatar and Details (SSO-Flutter style)
          CircleAvatar(
            radius: 30,
            backgroundColor: AppColors.primary,
            child: Text(
              _societyNameController.text.isNotEmpty
                  ? _societyNameController.text[0].toUpperCase()
                  : 'G',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Society Name (Large heading)
          Text(
            _societyNameController.text.isNotEmpty
                ? _societyNameController.text.toUpperCase()
                : 'SOCIETY NAME',
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),

          // Masked Account Number
          Text(
            _accountNumberController.text.isNotEmpty
                ? '******${_accountNumberController.text.substring(_accountNumberController.text.length - 4)}'
                : '******0000',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 24),

          // Large Amount Display
          RichText(
            text: TextSpan(
              text: '₹ ',
              style: TextStyle(
                fontSize: 32,
                color: Colors.grey.shade600,
              ),
              children: [
                TextSpan(
                  text:
                      _amountCalculation?['amount']?.toStringAsFixed(0) ?? '0',
                  style: const TextStyle(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),

          // Options List (SSO-Flutter style)
          Card(
            child: Column(
              children: [
                // Add a note
                ListTile(
                  leading: const Icon(Icons.note_add),
                  title: const Text('Add a note'),
                  subtitle: _noteController.text.isNotEmpty
                      ? Text(_noteController.text)
                      : null,
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showNoteDialog(),
                ),
                const Divider(height: 1),

                // Add society PAN number
                ListTile(
                  leading: const Icon(Icons.fact_check),
                  title: const Text('Add society PAN number'),
                  subtitle: Text(
                    _panController.text.isEmpty
                        ? 'Optional'
                        : _panController.text,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showPanDialog(),
                ),
                const Divider(height: 1),

                // Total amount
                ListTile(
                  leading: const Icon(Icons.receipt_long),
                  title: Text(
                    'Total amount ₹${_amountCalculation?['amount']?.toStringAsFixed(0) ?? '0'}',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  subtitle: Text(
                    'Includes processing fees',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                  onTap: () => _showAmountBreakdown(),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Terms and Conditions
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Checkbox(
                  value: _termsAccepted,
                  onChanged: (value) {
                    setState(() {
                      _termsAccepted = value ?? false;
                    });
                  },
                  activeColor: AppColors.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _termsAccepted = !_termsAccepted;
                      });
                    },
                    child: const Text(
                      'I agree to the terms and conditions for this maintenance payment. I understand that this payment is non-refundable once processed.',
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Processing Notice (SSO-Flutter style)
          Text(
            'It may take up to 48 working hours for the amount to be reflected in the bank account.',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  /// Show amount breakdown dialog (SSO-Flutter style)
  void _showAmountBreakdown() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Amount Details',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Total Amount'),
                Text(
                  '₹${_amountCalculation?['amount']?.toStringAsFixed(2) ?? '0.00'}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Processing Fees',
                  style: TextStyle(color: Colors.grey.shade600),
                ),
                Text(
                  '₹${_amountCalculation?['processing_fee']?.toStringAsFixed(2) ?? '0.00'}',
                  style: TextStyle(color: Colors.grey.shade600),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep != MaintenanceStep.ACCOUNT_DETAILS)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: BorderSide(color: AppColors.primary, width: 2),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Previous',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          if (_currentStep != MaintenanceStep.ACCOUNT_DETAILS)
            const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: (_getNextButtonAction() == null ||
                      _isProcessingPayment ||
                      _isCalculating ||
                      _isLoadingAccount)
                  ? null
                  : _getNextButtonAction(),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                disabledBackgroundColor: Colors.grey[300],
                disabledForegroundColor: Colors.grey[600],
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: Text(
                _getNextButtonLabel(),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _processPayment() async {
    if (!_termsAccepted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please accept the terms and conditions to proceed'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isPaymentProcessing = true;
      _currentStep = MaintenanceStep.PAYMENT_PROCESSING;
    });

    try {
      // Debug logging for payment initiation
      final baseAmount = double.tryParse(_amountController.text) ?? 0;
      print('🔍 [PAYMENT DEBUG] Initiating payment with:');
      print('  - Base Amount: $baseAmount');
      print('  - Total Payable: $_totalPayableAmount');
      print('  - Account Details: $_accountDetails');
      print(
          '  - Account Name: ${_accountDetails?['account_details']?['business_name']}');
      print('  - Account ID: ${_accountDetails?['account_id']}');
      print('  - PAN: ${_panController.text}');
      print('  - Note: ${_noteController.text}');

      // Initiate payment with SSO-Flutter service
      final paymentData =
          await SSOFlutterMaintenanceService.initiateSocietyPayment(
        accountName:
            _accountDetails?['account_details']?['business_name'] ?? 'Society',
        totalPayableAmount: _totalPayableAmount,
        actualAmount: baseAmount,
        accountId: _accountDetails?['account_id'] ?? '',
        pan: _panController.text.isNotEmpty ? _panController.text : null,
        note: _noteController.text.isNotEmpty ? _noteController.text : null,
      );

      print('✅ [PAYMENT DEBUG] Payment initiated successfully: $paymentData');

      // Configure Razorpay options
      var options = {
        'key': 'rzp_test_1DP5mmOlF5G5ag', // Replace with your Razorpay key
        'amount': (_totalPayableAmount * 100).toInt(), // Amount in paise
        'name': 'CHSOne Maintenance Payment',
        'description':
            'Maintenance payment for ${_accountDetails?['account_details']?['business_name'] ?? 'Society'}',
        'order_id': paymentData['order_id'],
        'prefill': {
          'contact': _accountDetails?['phone'] ?? '',
          'email': _accountDetails?['email'] ?? _emailController.text,
        },
        'theme': {
          'color': '#2196F3',
        },
      };

      _razorpay.open(options);
    } catch (e) {
      print('❌ Error initiating payment: $e');
      setState(() {
        _isPaymentProcessing = false;
        _currentStep = MaintenanceStep.PAYMENT_CONFIRMATION;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error initiating payment: $e'),
          backgroundColor: Colors.red,
          action: SnackBarAction(
            label: 'Retry',
            textColor: Colors.white,
            onPressed: () => _processPayment(),
          ),
        ),
      );
    }
  }

  String _getNextButtonLabel() {
    switch (_currentStep) {
      case MaintenanceStep.ACCOUNT_DETAILS:
        return 'Next';
      case MaintenanceStep.PAYMENT_CONFIRMATION:
        return 'Pay Now';
      case MaintenanceStep.PAYMENT_PROCESSING:
        return 'Processing...';
    }
  }

  VoidCallback? _getNextButtonAction() {
    if (_isProcessingPayment || _isCalculating || _isLoadingAccount) {
      return null;
    }

    switch (_currentStep) {
      case MaintenanceStep.ACCOUNT_DETAILS:
        return (_accountDetails != null && _isAmountValid) ? _nextStep : null;
      case MaintenanceStep.PAYMENT_CONFIRMATION:
        return _termsAccepted ? _nextStep : null;
      case MaintenanceStep.PAYMENT_PROCESSING:
        return null;
    }
  }

  void _showSuccessDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.green, size: 28),
            const SizedBox(width: 8),
            const Text('Payment Successful'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Transaction ID: ${result['transaction_id'] ?? 'N/A'}'),
            Text(
                'Amount: ₹${_amountCalculation?['amount']?.toStringAsFixed(2) ?? '0.00'}'),
            const SizedBox(height: 8),
            const Text('Your payment has been processed successfully.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/dashboard');
            },
            child: const Text('Done'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red, size: 28),
            SizedBox(width: 8),
            Text('Payment Failed'),
          ],
        ),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _currentStep = MaintenanceStep.PAYMENT_CONFIRMATION;
              });
            },
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }
}
