import 'dart:developer';
import 'dart:convert';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import '../../../core/models/api_response.dart';
import '../../../core/services/auth_token_manager.dart';
import '../../../core/services/chsone_operator_headers.dart';
import '../models/maintenance_api_models.dart' as api_models;
import 'maintenance_api_service.dart';

/// Maintenance Payment Processor with Razorpay Integration
/// Matches SSO-Flutter's payment processing functionality
class MaintenancePaymentProcessor {
  static const String _serviceName = 'MaintenancePaymentProcessor';
  
  // Razorpay configuration (SSO-Flutter compatible)
  static const String _razorpayKeyId = 'rzp_test_DCmn2Xc0a7coSH'; // SSO-Flutter test key
  static const String _razorpayKeySecret = 'your_key_secret'; // TODO: Replace with actual secret
  
  // CHSONE payment endpoints (matching SSO-Flutter)
  static const String _chsoneOperatorsBaseUrl = 'https://chsone.in/api/v1/operators/';
  static const String _initiatePaymentEndpoint = 'initiateSocietyPayment';
  static const String _completePaymentEndpoint = 'completeSocietyPayment';
  
  final Razorpay _razorpay;
  final MaintenanceApiService _apiService;
  
  // Payment state management
  api_models.MaintenancePaymentRequest? _currentPaymentRequest;
  Function(api_models.MaintenancePaymentResponse)? _onPaymentSuccess;
  Function(String)? _onPaymentError;
  
  MaintenancePaymentProcessor({
    MaintenanceApiService? apiService,
  }) : _apiService = apiService ?? MaintenanceApiService(),
       _razorpay = Razorpay() {
    _initializeRazorpay();
  }
  
  /// Initialize Razorpay event listeners
  void _initializeRazorpay() {
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
    
    log('✅ [$_serviceName] Razorpay initialized successfully');
  }
  
  /// Process maintenance payment with Razorpay integration
  /// Matches SSO-Flutter's payBill() functionality
  Future<ApiResponse<api_models.MaintenancePaymentResponse>> processPayment({
    required api_models.MaintenancePaymentRequest request,
    required Function(api_models.MaintenancePaymentResponse) onSuccess,
    required Function(String) onError,
  }) async {
    try {
      log('💳 [$_serviceName] Starting payment process for amount: ${request.totalAmount}');
      
      // Store callbacks for later use
      _currentPaymentRequest = request;
      _onPaymentSuccess = onSuccess;
      _onPaymentError = onError;
      
      // Step 1: Calculate payment amount
      final calculationResponse = await _apiService.calculatePaymentAmount(
        amount: request.actualAmount,
      );
      
      if (!calculationResponse.success || calculationResponse.data == null) {
        final error = 'Failed to calculate payment amount: ${calculationResponse.message}';
        log('❌ [$_serviceName] $error');
        onError(error);
        return ApiResponse.error(error);
      }
      
      final calculation = calculationResponse.data!;
      log('🧮 [$_serviceName] Calculated total: ${calculation.totalAmount}');
      
      // Step 2: Initiate payment with CHSONE
      final initiateResponse = await _initiatePaymentWithChsone(request, calculation);
      
      if (!initiateResponse.success || initiateResponse.data == null) {
        final error = 'Failed to initiate payment: ${initiateResponse.message}';
        log('❌ [$_serviceName] $error');
        onError(error);
        return ApiResponse.error(error);
      }
      
      final paymentResponse = initiateResponse.data!;
      log('🚀 [$_serviceName] Payment initiated with ID: ${paymentResponse.paymentId}');
      
      // Step 3: Launch Razorpay checkout
      await _launchRazorpayCheckout(paymentResponse, calculation);
      
      // Return success - actual completion happens in Razorpay callbacks
      return ApiResponse.success(
        paymentResponse,
        message: 'Payment initiated successfully. Waiting for user completion.',
      );
      
    } catch (e) {
      final error = 'Payment processing failed: $e';
      log('❌ [$_serviceName] $error');
      onError(error);
      return ApiResponse.error(error);
    }
  }
  
  /// Initiate payment with CHSONE (SSO-Flutter compatible)
  Future<ApiResponse<api_models.MaintenancePaymentResponse>> _initiatePaymentWithChsone(
    api_models.MaintenancePaymentRequest request,
    api_models.MaintenanceCalculation calculation,
  ) async {
    try {
      final token = await _getAuthToken();
      if (token == null) {
        throw Exception('Authentication required');
      }
      
      // Prepare SSO-Flutter compatible payload
      final payload = {
        'account_name': request.accountName,
        'total_payable_amt': calculation.totalAmount.toString(),
        'actual_amt': request.actualAmount.toString(),
        'account_id': request.accountId,
        'pan': request.pan ?? '',
        'note': request.note ?? '',
        'token': token,
      };
      
      log('📡 [$_serviceName] Initiating payment with CHSONE...');
      log('🔗 [$_serviceName] URL: $_chsoneOperatorsBaseUrl$_initiatePaymentEndpoint');
      
      final headers = await ChsOneOperatorHeaders.build();
      headers['Content-Type'] = 'application/json';
      
      final response = await _makeHttpRequest(
        url: '$_chsoneOperatorsBaseUrl$_initiatePaymentEndpoint',
        method: 'POST',
        headers: headers,
        body: json.encode(payload),
      );
      
      if (response['success'] == true) {
        log('✅ [$_serviceName] Payment initiated successfully');
        
        // Create payment response from CHSONE response
        final paymentResponse = api_models.MaintenancePaymentResponse(
          paymentId: response['payment_id'] ?? response['id'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
          transactionId: response['transaction_id'] ?? response['txn_id'] ?? '',
          status: 'initiated',
          amount: calculation.totalAmount,
          timestamp: DateTime.now(),
          gatewayResponse: json.encode(response),
          paymentDetails: {
            'order_id': response['order_id'],
            'razorpay_key': _razorpayKeyId,
            'calculation': calculation.toJson(),
            'original_request': request.toJson(),
          },
        );
        
        return ApiResponse.success(paymentResponse);
      } else {
        throw Exception('CHSONE initiation failed: ${response['message'] ?? 'Unknown error'}');
      }
      
    } catch (e) {
      log('❌ [$_serviceName] CHSONE initiation error: $e');
      return ApiResponse.error('Failed to initiate payment with CHSONE: $e');
    }
  }
  
  /// Launch Razorpay checkout (SSO-Flutter compatible)
  Future<void> _launchRazorpayCheckout(
    api_models.MaintenancePaymentResponse paymentResponse,
    api_models.MaintenanceCalculation calculation,
  ) async {
    try {
      final userData = await AuthTokenManager.getUserData();
      
      final options = {
        'key': _razorpayKeyId,
        'amount': (calculation.totalAmount * 100).toInt(), // Amount in paise
        'name': 'Maintenance Payment',
        'description': 'Society Maintenance Payment',
        'order_id': paymentResponse.paymentDetails['order_id'],
        'prefill': {
          'contact': userData?['mobile'] ?? '',
          'email': userData?['email'] ?? '',
          'name': userData?['name'] ?? _currentPaymentRequest?.accountName ?? '',
        },
        'theme': {
          'color': '#3399cc',
        },
        'modal': {
          'backdropclose': false,
          'escape': false,
          'handleback': false,
          'ondismiss': () {
            log('⚠️ [$_serviceName] Payment cancelled by user');
            _onPaymentError?.call('Payment cancelled by user');
          },
        },
        'retry': {
          'enabled': true,
          'max_count': 3
        },
        'timeout': 300, // 5 minutes
      };
      
      log('🚀 [$_serviceName] Launching Razorpay checkout...');
      log('💰 [$_serviceName] Amount: ₹${calculation.totalAmount}');
      
      _razorpay.open(options);
      
    } catch (e) {
      log('❌ [$_serviceName] Razorpay launch error: $e');
      _onPaymentError?.call('Failed to launch payment gateway: $e');
    }
  }
  
  /// Handle Razorpay payment success
  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    try {
      log('✅ [$_serviceName] Razorpay payment successful');
      log('🔑 [$_serviceName] Payment ID: ${response.paymentId}');
      log('🔑 [$_serviceName] Order ID: ${response.orderId}');
      log('🔑 [$_serviceName] Signature: ${response.signature}');
      
      if (_currentPaymentRequest == null) {
        throw Exception('No current payment request found');
      }
      
      // Complete payment with CHSONE
      final completeRequest = api_models.CompletePaymentRequest(
        paymentId: response.orderId ?? '',
        transactionId: response.paymentId ?? '',
        status: 'success',
        amount: _currentPaymentRequest!.totalAmount,
        paymentDetails: {
          'razorpay_payment_id': response.paymentId,
          'razorpay_order_id': response.orderId,
          'razorpay_signature': response.signature,
        },
      );
      
      final completionResponse = await _completePaymentWithChsone(completeRequest);
      
      if (completionResponse.success && completionResponse.data != null) {
        log('🎉 [$_serviceName] Payment completed successfully');
        _onPaymentSuccess?.call(completionResponse.data!);
      } else {
        throw Exception('Payment completion failed: ${completionResponse.message}');
      }
      
    } catch (e) {
      log('❌ [$_serviceName] Payment success handling error: $e');
      _onPaymentError?.call('Payment completion failed: $e');
    } finally {
      _clearPaymentState();
    }
  }
  
  /// Handle Razorpay payment error
  void _handlePaymentError(PaymentFailureResponse response) {
    try {
      log('❌ [$_serviceName] Razorpay payment failed');
      log('🔍 [$_serviceName] Error code: ${response.code}');
      log('🔍 [$_serviceName] Error message: ${response.message}');
      
      final errorMessage = 'Payment failed: ${response.message} (Code: ${response.code})';
      _onPaymentError?.call(errorMessage);
      
    } catch (e) {
      log('❌ [$_serviceName] Payment error handling failed: $e');
      _onPaymentError?.call('Payment failed with unknown error');
    } finally {
      _clearPaymentState();
    }
  }
  
  /// Handle external wallet selection
  void _handleExternalWallet(ExternalWalletResponse response) {
    log('📱 [$_serviceName] External wallet selected: ${response.walletName}');
    // Handle external wallet if needed
  }
  
  /// Complete payment with CHSONE (SSO-Flutter compatible)
  Future<ApiResponse<api_models.MaintenancePaymentResponse>> _completePaymentWithChsone(
    api_models.CompletePaymentRequest request,
  ) async {
    try {
      final token = await _getAuthToken();
      if (token == null) {
        throw Exception('Authentication required');
      }
      
      final payload = {
        'payment_id': request.paymentId,
        'transaction_id': request.transactionId,
        'status': request.status,
        'amount': request.amount.toString(),
        'payment_details': request.paymentDetails,
        'token': token,
      };
      
      log('📡 [$_serviceName] Completing payment with CHSONE...');
      
      final headers = await ChsOneOperatorHeaders.build();
      headers['Content-Type'] = 'application/json';
      
      final response = await _makeHttpRequest(
        url: '$_chsoneOperatorsBaseUrl$_completePaymentEndpoint',
        method: 'POST',
        headers: headers,
        body: json.encode(payload),
      );
      
      if (response['success'] == true) {
        log('✅ [$_serviceName] Payment completed successfully');
        
        final completedPayment = api_models.MaintenancePaymentResponse(
          paymentId: request.paymentId,
          transactionId: request.transactionId,
          status: 'completed',
          amount: request.amount,
          timestamp: DateTime.now(),
          gatewayResponse: json.encode(response),
          paymentDetails: request.paymentDetails,
        );
        
        return ApiResponse.success(completedPayment);
      } else {
        throw Exception('CHSONE completion failed: ${response['message'] ?? 'Unknown error'}');
      }
      
    } catch (e) {
      log('❌ [$_serviceName] CHSONE completion error: $e');
      return ApiResponse.error('Failed to complete payment with CHSONE: $e');
    }
  }
  
  /// Get authentication token
  Future<String?> _getAuthToken() async {
    return await AuthTokenManager.getBestAvailableToken();
  }
  
  /// Make HTTP request (placeholder - implement with your HTTP client)
  Future<Map<String, dynamic>> _makeHttpRequest({
    required String url,
    required String method,
    required Map<String, String> headers,
    String? body,
  }) async {
    // TODO: Implement with your preferred HTTP client (http, dio, etc.)
    // This is a placeholder implementation
    throw UnimplementedError('HTTP client implementation needed');
  }
  
  /// Clear payment state
  void _clearPaymentState() {
    _currentPaymentRequest = null;
    _onPaymentSuccess = null;
    _onPaymentError = null;
  }
  
  /// Dispose resources
  void dispose() {
    _razorpay.clear();
    _clearPaymentState();
    log('🧹 [$_serviceName] Resources disposed');
  }
}
